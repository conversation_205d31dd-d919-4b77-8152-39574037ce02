package com.unity3d.player;

import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import java.io.File;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import androidx.core.content.FileProvider;
import androidx.lifecycle.Observer;
import androidx.work.BackoffPolicy;
import androidx.work.Constraints;
import androidx.work.Data;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkInfo;
import androidx.work.WorkManager;

/**
 * Modern update manager with improved error handling, retry mechanism, and UI
 * Replaces the legacy UpdateManager with modern Android components
 */
public class ModernUpdateManager {
    
    private static final String TAG = "ModernUpdateManager";
    private static final String DEFAULT_APK_NAME = "app-update.apk";
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long INITIAL_BACKOFF_DELAY = 10; // seconds
    
    private Context context;
    private String downloadUrl;
    private String version;
    private String content;
    private ModernProgressDialog progressDialog;
    private NetworkMonitor networkMonitor;
    private Handler mainHandler;
    private UUID currentWorkId;
    private Observer<WorkInfo> workObserver;
    
    // Retry mechanism
    private int retryCount = 0;
    private boolean isRetryEnabled = true;
    
    // Callbacks
    private UpdateCallback updateCallback;
    
    public interface UpdateCallback {
        void onUpdateStarted();
        void onProgressUpdate(int progress);
        void onUpdateCompleted(String filePath);
        void onUpdateFailed(String error);
        void onUpdateCancelled();
    }
    
    public ModernUpdateManager(Context context) {
        this.context = context;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.networkMonitor = new NetworkMonitor(context);
        setupNetworkMonitoring();
    }
    
    private void setupNetworkMonitoring() {
        networkMonitor.startMonitoring(new NetworkMonitor.NetworkStateListener() {
            @Override
            public void onNetworkAvailable() {
                // Network is back, we could resume download if needed
                if (progressDialog != null && progressDialog.isShowing()) {
                    mainHandler.post(() -> {
                        progressDialog.setMessage("Network restored. Continuing download...");
                    });
                }
            }
            
            @Override
            public void onNetworkLost() {
                // Network lost during download
                if (progressDialog != null && progressDialog.isShowing()) {
                    mainHandler.post(() -> {
                        progressDialog.setMessage("Network lost. Will retry when connection is restored...");
                    });
                }
            }
        });
    }
    
    public void setDownloadUrl(String url) {
        this.downloadUrl = url;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setContent(String content) {
        this.content = content;
    }
    
    public void setUpdateCallback(UpdateCallback callback) {
        this.updateCallback = callback;
    }
    
    public void setRetryEnabled(boolean enabled) {
        this.isRetryEnabled = enabled;
    }
    
    /**
     * Check for updates and show dialog
     */
    public void checkUpdate(boolean isForced) {
        if (!networkMonitor.isNetworkAvailable()) {
            showToast("Network is unavailable. Please check your connection.");
            if (updateCallback != null) {
                updateCallback.onUpdateFailed("No network connection");
            }
            return;
        }
        
        showUpdateDialog(isForced);
    }
    
    private void showUpdateDialog(boolean isForced) {
//        AlertDialog.Builder builder = new AlertDialog.Builder(context)
//                .setTitle("New Version Available")
//                .setMessage("A new version is available. Would you like to update now?")
//                .setPositiveButton("Update", (dialog, which) -> startDownload());
//
//        if (!isForced) {
//            builder.setNegativeButton("Later", (dialog, which) -> {
//                if (updateCallback != null) {
//                    updateCallback.onUpdateCancelled();
//                }
//            });
//        }
//
//        builder.setCancelable(!isForced);
//        builder.show();

        UpdateDialog updateDialog = new UpdateDialog(context);
        TextView tv_version = updateDialog.findViewById(R.id.tv_version);
        TextView tv_update_content = updateDialog.findViewById(R.id.tv_update_content);
        tv_version.setText("V"+version);
        tv_update_content.setText(content);
        Button btnUpdateNow = updateDialog.findViewById(R.id.btn_update_now);
        Button btnNotNow = updateDialog.findViewById(R.id.btn_not_now);
        LinearLayout notupdate_layout = updateDialog.findViewById(R.id.notupdate_layout);
        Button ex_now = updateDialog.findViewById(R.id.ex_now);
        if (isForced) {
            notupdate_layout.setVisibility(View.GONE);
            ex_now.setVisibility(View.VISIBLE);
        } else {
            notupdate_layout.setVisibility(View.VISIBLE);
            ex_now.setVisibility(View.GONE);
        }
        btnUpdateNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateDialog.dismiss();
                startDownload();
            }
        });
        ex_now.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateDialog.dismiss();
                startDownload();
            }
        });
        btnNotNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (updateCallback != null) {
                    updateCallback.onUpdateCancelled();
                }
                updateDialog.dismiss();
            }
        });
        updateDialog.show();
    }
    
    /**
     * Start the download process using WorkManager
     */
    private void startDownload() {
        if (downloadUrl == null || downloadUrl.isEmpty()) {
            showToast("Download URL not set");
            if (updateCallback != null) {
                updateCallback.onUpdateFailed("Download URL not set");
            }
            return;
        }
        
        // Cancel any existing download
        cancelCurrentDownload();
        
        // Reset retry count
        retryCount = 0;
        
        // Show progress dialog
        showProgressDialog();
        
        // Start download work
        startDownloadWork();
        
        if (updateCallback != null) {
            updateCallback.onUpdateStarted();
        }
    }
    
    private void startDownloadWork() {
        // Create input data
        Data inputData = new Data.Builder()
                .putString(DownloadWorker.KEY_DOWNLOAD_URL, downloadUrl)
                .putString(DownloadWorker.KEY_FILE_NAME, DEFAULT_APK_NAME)
                .build();
        
        // Create constraints
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(false)
                .build();
        
        // Create work request with retry policy
        OneTimeWorkRequest downloadRequest = new OneTimeWorkRequest.Builder(DownloadWorker.class)
                .setInputData(inputData)
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    INITIAL_BACKOFF_DELAY,
                    TimeUnit.SECONDS
                )
                .build();
        
        currentWorkId = downloadRequest.getId();
        
        // Enqueue work
        WorkManager.getInstance(context).enqueue(downloadRequest);
        
        // Observe work progress
        observeWorkProgress();
    }
    
    private void observeWorkProgress() {
        if (currentWorkId == null) return;
        
        workObserver = new Observer<WorkInfo>() {
            @Override
            public void onChanged(WorkInfo workInfo) {
                if (workInfo == null) return;
                
                handleWorkInfoUpdate(workInfo);
            }
        };
        
        WorkManager.getInstance(context)
                .getWorkInfoByIdLiveData(currentWorkId)
                .observeForever(workObserver);
    }
    
    private void handleWorkInfoUpdate(WorkInfo workInfo) {
        switch (workInfo.getState()) {
            case RUNNING:
                Data progress = workInfo.getProgress();
                int progressValue = progress.getInt(DownloadWorker.KEY_PROGRESS, 0);
                updateProgress(progressValue);
                break;
                
            case SUCCEEDED:
                Data outputData = workInfo.getOutputData();
                String filePath = outputData.getString(DownloadWorker.KEY_FILE_PATH);
                handleDownloadSuccess(filePath);
                break;
                
            case FAILED:
                Data errorData = workInfo.getOutputData();
                String errorMessage = errorData.getString(DownloadWorker.KEY_ERROR_MESSAGE);
                handleDownloadFailure(errorMessage);
                break;
                
            case CANCELLED:
                handleDownloadCancellation();
                break;
        }
    }
    
    private void updateProgress(int progress) {
        mainHandler.post(() -> {
            if (progressDialog != null) {
                progressDialog.setProgress(progress);
            }
            if (updateCallback != null) {
                updateCallback.onProgressUpdate(progress);
            }
        });
    }
    
    private void handleDownloadSuccess(String filePath) {
        mainHandler.post(() -> {
            dismissProgressDialog();
            if (filePath != null) {
                installApk(filePath);
                if (updateCallback != null) {
                    updateCallback.onUpdateCompleted(filePath);
                }
            }
        });
        cleanupObserver();
    }
    
    private void handleDownloadFailure(String errorMessage) {
        mainHandler.post(() -> {
            if (isRetryEnabled && retryCount < MAX_RETRY_ATTEMPTS) {
                retryCount++;
                showRetryDialog(errorMessage);
            } else {
                dismissProgressDialog();
                showToast("Download failed: " + (errorMessage != null ? errorMessage : "Unknown error"));
                if (updateCallback != null) {
                    updateCallback.onUpdateFailed(errorMessage);
                }
            }
        });
        cleanupObserver();
    }
    
    private void handleDownloadCancellation() {
        mainHandler.post(() -> {
            dismissProgressDialog();
            if (updateCallback != null) {
                updateCallback.onUpdateCancelled();
            }
        });
        cleanupObserver();
    }

    private void showRetryDialog(String errorMessage) {
        String message = String.format("Download failed: %s\n\nRetry attempt %d of %d",
                errorMessage != null ? errorMessage : "Unknown error",
                retryCount,
                MAX_RETRY_ATTEMPTS);

        new AlertDialog.Builder(context)
                .setTitle("Download Failed")
                .setMessage(message)
                .setPositiveButton("Retry", (dialog, which) -> {
                    // Retry download
                    startDownloadWork();
                })
                .setNegativeButton("Cancel", (dialog, which) -> {
                    dismissProgressDialog();
                    if (updateCallback != null) {
                        updateCallback.onUpdateFailed("User cancelled after retry");
                    }
                })
                .setCancelable(false)
                .show();
    }

    private void showProgressDialog() {
        if (progressDialog == null) {
            progressDialog = new ModernProgressDialog(context);
            progressDialog.setTitle("Downloading Update");
            progressDialog.setMessage("Please wait while the update is being downloaded...");
            progressDialog.setCancelable(false);

            // Set custom progress bar colors
            // You can customize these colors as needed
            progressDialog.setProgressColor(0xFF4A73FD);        // Green progress
            progressDialog.setProgressBackgroundColor(0xFFE0E0E0); // Light gray background

//            progressDialog.setOnCancelListener(() -> {
//                cancelCurrentDownload();
//                if (updateCallback != null) {
//                    updateCallback.onUpdateCancelled();
//                }
//            });
        }

        progressDialog.setProgress(0);
        progressDialog.show();
    }

    private void dismissProgressDialog() {
        if (progressDialog != null && progressDialog.isShowing()) {
            progressDialog.dismiss();
        }
    }

    private void cancelCurrentDownload() {
        if (currentWorkId != null) {
            WorkManager.getInstance(context).cancelWorkById(currentWorkId);
            currentWorkId = null;
        }
        cleanupObserver();
    }

    private void cleanupObserver() {
        if (workObserver != null && currentWorkId != null) {
            WorkManager.getInstance(context)
                    .getWorkInfoByIdLiveData(currentWorkId)
                    .removeObserver(workObserver);
            workObserver = null;
        }
    }

    private void installApk(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                showToast("Downloaded file not found");
                return;
            }

            Intent intent = new Intent(Intent.ACTION_VIEW);
            Uri fileUri = getFileUri(file);
            intent.setDataAndType(fileUri, "application/vnd.android.package-archive");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

            context.startActivity(intent);

        } catch (Exception e) {
            showToast("Failed to install APK: " + e.getMessage());
            if (updateCallback != null) {
                updateCallback.onUpdateFailed("Installation failed: " + e.getMessage());
            }
        }
    }

    private Uri getFileUri(File file) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return FileProvider.getUriForFile(context,
                    context.getPackageName() + ".fileprovider", file);
        } else {
            return Uri.fromFile(file);
        }
    }

    private void showToast(String message) {
        mainHandler.post(() ->
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        );
    }

    /**
     * Clean up resources when the manager is no longer needed
     */
    public void cleanup() {
        cancelCurrentDownload();
        dismissProgressDialog();
        networkMonitor.stopMonitoring();

        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
    }

    /**
     * Get current network status
     */
    public boolean isNetworkAvailable() {
        return networkMonitor.isNetworkAvailable();
    }

    /**
     * Get network type description
     */
    public String getNetworkType() {
        return networkMonitor.getNetworkType();
    }
}
