<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="600dp"
    android:layout_height="300dp"
    android:orientation="vertical">

    <LinearLayout
        android:background="@drawable/top_img"
        android:layout_width="600dp"
        android:layout_height="100dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="30dp"
            android:text="VERSION UPDATE"
            android:textColor="#ffffff"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="6dp"
            android:text=""
            android:textColor="#ffffff"
            android:textSize="15sp" />

    </LinearLayout>

    <LinearLayout
        android:background="@drawable/buttom_img"
        android:layout_width="600dp"
        android:layout_height="200dp"
        android:orientation="vertical">

        <TextView
            android:textColor="#313131"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="15dp"
            android:text="UPDATED CONTENT:"
            android:textSize="20sp" />

        <TextView
            android:textColor="#313131"
            android:id="@+id/tv_update_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:text=""
            android:textSize="16sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_marginTop="20dp"
            android:id="@+id/notupdate_layout"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="60dp"
            android:layout_marginRight="60dp"
            android:orientation="horizontal">

            <Button
                android:background="@drawable/not_now_button_focus_selector"
                android:textSize="20sp"
                android:textColor="@drawable/not_not_button_text_color_selector"
                android:id="@+id/btn_not_now"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:text="Not Now" />

            <Button
                android:id="@+id/btn_update_now"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:background="@drawable/button_focus_selector"
                android:text="Update Now"
                android:textColor="@drawable/button_text_color_selector"
                android:textSize="20sp" />
        </LinearLayout>


        <Button
            android:visibility="gone"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:id="@+id/ex_now"
            android:layout_width="300dp"
            android:layout_height="30dp"
            android:background="@drawable/qg_button_focus_selector"
            android:text="Experience it Now"
            android:textColor="@drawable/button_text_color_selector"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>