// GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN

allprojects {
    buildscript {
        repositories {
            mavenCentral()
            google()
            jcenter()
            maven { url 'https://repo1.maven.org/maven2/' }
        }

        dependencies {
            classpath 'com.android.tools.build:gradle:4.2.1'

        }
    }

    repositories {
        mavenCentral()
        google()
        jcenter()
        flatDir {
            dirs "${project(':unityLibrary').projectDir}/libs"
        }
        maven { url 'https://repo1.maven.org/maven2/' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
