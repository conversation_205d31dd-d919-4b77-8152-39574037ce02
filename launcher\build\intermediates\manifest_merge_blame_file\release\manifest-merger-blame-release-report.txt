1<?xml version="1.0" encoding="utf-8"?>
2<!-- GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN -->
3<manifest xmlns:android="http://schemas.android.com/apk/res/android"
4    package="com.cy.exercisetv"
5    android:installLocation="preferExternal"
6    android:versionCode="31"
7    android:versionName="1.3.2" >
8
9    <uses-sdk
10        android:minSdkVersion="21"
10-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml
11        android:targetSdkVersion="30" />
11-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml
12
13    <supports-screens
13-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:3-163
14        android:anyDensity="true"
14-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:135-160
15        android:largeScreens="true"
15-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:78-105
16        android:normalScreens="true"
16-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:49-77
17        android:smallScreens="true"
17-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:21-48
18        android:xlargeScreens="true" />
18-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:106-134
19
20    <uses-feature android:glEsVersion="0x00030000" />
20-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:5-54
20-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:19-51
21    <uses-feature
21-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:5-14:36
22        android:name="android.hardware.vulkan.version"
22-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:9-55
23        android:required="false" />
23-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:9-33
24    <uses-feature
24-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:5-17:36
25        android:name="android.hardware.camera"
25-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:9-47
26        android:required="false" />
26-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:9-33
27    <uses-feature
27-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:18:5-20:36
28        android:name="android.hardware.camera.autofocus"
28-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:19:9-57
29        android:required="false" />
29-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:9-33
30    <uses-feature
30-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:21:5-23:36
31        android:name="android.hardware.camera.front"
31-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:9-53
32        android:required="false" />
32-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:23:9-33
33    <uses-feature
33-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:24:5-26:36
34        android:name="android.hardware.touchscreen"
34-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:9-52
35        android:required="false" />
35-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:26:9-33
36    <uses-feature
36-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:5-29:36
37        android:name="android.hardware.touchscreen.multitouch"
37-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:28:9-63
38        android:required="false" />
38-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:9-33
39    <uses-feature
39-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:30:5-32:36
40        android:name="android.hardware.touchscreen.multitouch.distinct"
40-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:31:9-72
41        android:required="false" />
41-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:32:9-33
42
43    <uses-permission android:name="android.permission.INTERNET" />
43-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:34:5-67
43-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:34:22-64
44    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
44-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:5-81
44-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:22-78
45    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
45-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:5-83
45-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:22-80
46    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
46-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:37:5-79
46-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:37:22-76
47    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
47-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:38:5-76
47-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:38:22-73
48    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
48-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:39:5-75
48-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:39:22-72
49    <uses-permission android:name="android.permission.CAMERA" />
49-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:40:5-65
49-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:40:22-62
50    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
50-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:5-76
50-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:22-73
51    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
51-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:42:5-79
51-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:42:22-76
52    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
52-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:43:5-81
52-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:43:22-78
53    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
53-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:44:5-79
53-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:44:22-76
54    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
54-->[com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:5-79
54-->[com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:22-76
55
56    <application
56-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:5:3-83
57        android:name="com.unity3d.player.App"
57-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:46:18-55
58        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
58-->[androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:24:18-86
59        android:icon="@mipmap/app_icon"
59-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:5:49-80
60        android:label="@string/app_name" >
60-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:5:16-48
61        <activity
61-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:47:9-74:20
62            android:name="com.unity3d.player.UnityPlayerActivity"
62-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:48:13-66
63            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
63-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:49:13-194
64            android:exported="true"
64-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:50:13-36
65            android:hardwareAccelerated="false"
65-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:51:13-48
66            android:launchMode="singleTask"
66-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:52:13-44
67            android:resizeableActivity="false"
67-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:53:13-47
68            android:screenOrientation="landscape"
68-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:54:13-50
69            android:theme="@style/UnityThemeSelector" >
69-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:55:13-54
70            <intent-filter android:priority="1000" >
70-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:56:13-62:29
70-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:56:28-51
71                <action android:name="android.intent.action.MAIN" />
71-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:57:17-69
71-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:57:25-66
72
73                <category android:name="android.intent.category.LAUNCHER" />
73-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:59:17-77
73-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:59:27-74
74                <category android:name="android.intent.category.HOME" />
74-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:60:17-73
74-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:60:27-70
75                <category android:name="android.intent.category.DEFAULT" />
75-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:61:17-76
75-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:61:27-73
76            </intent-filter>
77            <!-- <intent-filter> -->
78            <!-- <action android:name="android.intent.action.MAIN" /> -->
79            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
80            <!-- </intent-filter> -->
81
82            <meta-data
82-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:68:13-70:40
83                android:name="unityplayer.UnityActivity"
83-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:69:17-57
84                android:value="true" />
84-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:70:17-37
85            <meta-data
85-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:71:13-73:40
86                android:name="android.notch_support"
86-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:72:17-53
87                android:value="true" />
87-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:73:17-37
88        </activity>
89
90        <meta-data
90-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:76:9-78:33
91            android:name="unity.splash-mode"
91-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:77:13-45
92            android:value="0" />
92-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:78:13-30
93        <meta-data
93-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:79:9-81:36
94            android:name="unity.splash-enable"
94-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:80:13-47
95            android:value="True" />
95-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:81:13-33
96        <meta-data
96-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:82:9-84:37
97            android:name="unity.allow-resizable-window"
97-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:83:13-56
98            android:value="False" />
98-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:84:13-34
99        <meta-data
99-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:85:9-87:50
100            android:name="notch.config"
100-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:86:13-40
101            android:value="portrait|landscape" />
101-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:87:13-47
102        <meta-data
102-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:88:9-90:64
103            android:name="TEST_DEVICE_ID"
103-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:89:13-42
104            android:value="2A27406B9CA8A5472970D77E263F014F" />
104-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:90:13-61
105        <meta-data
105-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:91:9-93:68
106            android:name="unity.build-id"
106-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:92:13-42
107            android:value="3e5cc2c2-84b1-41e0-ae5f-809a2a5eda1f" />
107-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:93:13-65
108        <meta-data
108-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:94:9-96:56
109            android:name="UMENG_APPKEY"
109-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:95:13-40
110            android:value="686a18c779267e0210a1cb03" />
110-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:96:13-53
111        <meta-data
111-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:97:9-99:38
112            android:name="UMENG_CHANNEL"
112-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:98:13-41
113            android:value="uemeng" />
113-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:99:13-35
114
115        <provider
115-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:101:9-109:20
116            android:name="androidx.core.content.FileProvider"
116-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:102:13-62
117            android:authorities="com.cy.exercisetv.fileprovider"
117-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:103:13-64
118            android:exported="false"
118-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:104:13-37
119            android:grantUriPermissions="true" >
119-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:105:13-47
120            <meta-data
120-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:106:13-108:54
121                android:name="android.support.FILE_PROVIDER_PATHS"
121-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:107:17-67
122                android:resource="@xml/file_paths" />
122-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:108:17-51
123        </provider>
124
125        <service
125-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:10:9-14:41
126            android:name="com.efs.sdk.memleaksdk.monitor.UMonitorService"
126-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:11:13-74
127            android:enabled="true"
127-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:12:13-35
128            android:exported="false"
128-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:13:13-37
129            android:process=":u_heap" />
129-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:14:13-38
130    </application>
131
132</manifest>
