{"logs": [{"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-pl_values-pl.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-zu_values-zu.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-mr_values-mr.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-gu_values-gu.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-km_values-km.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-am_values-am.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-lt_values-lt.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ne_values-ne.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-gl_values-gl.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-pa_values-pa.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-uk_values-uk.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-lv_values-lv.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-kk_values-kk.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values_values.arsc.flat", "map": [{"source": "E:\\huoli-rk3566\\hl071501\\unityLibrary\\build\\intermediates\\packaged_res\\debug\\values\\values.xml", "from": {"startLines": "4", "startColumns": "4", "startOffsets": "155", "endLines": "21", "endColumns": "12", "endOffsets": "1057"}, "to": {"startLines": "89", "startColumns": "4", "startOffsets": "5335", "endLines": "106", "endColumns": "12", "endOffsets": "6237"}}, {"source": "E:\\huoli-rk3566\\hl071501\\launcher\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "5,2,7", "startColumns": "0,0,0", "startOffsets": "190,53,288", "endLines": "6,4,10", "endColumns": "8,8,8", "endOffsets": "286,188,513"}, "to": {"startLines": "87,118,121", "startColumns": "4,4,4", "startOffsets": "5235,7000,7138", "endLines": "88,120,124", "endColumns": "8,8,8", "endOffsets": "5330,7133,7360"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\083e340af32f320e6ad3917bcf6db07c\\work-runtime-2.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}}, {"source": "E:\\huoli-rk3566\\hl071501\\launcher\\src\\main\\res\\values\\ids.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "46", "endOffsets": "97"}, "to": {"startLines": "80", "startColumns": "4", "startOffsets": "4798", "endColumns": "45", "endOffsets": "4839"}}, {"source": "E:\\huoli-rk3566\\hl071501\\launcher\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,3", "startColumns": "2,2", "startOffsets": "55,103", "endColumns": "46,65", "endOffsets": "99,166"}, "to": {"startLines": "84,85", "startColumns": "4,4", "startOffsets": "5047,5096", "endColumns": "48,67", "endOffsets": "5091,5159"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\f6de2a670d04d6e6d777da00f7299d4c\\lifecycle-viewmodel-2.3.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "4844", "endColumns": "49", "endOffsets": "4889"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,83,84,88,89,90,91,98,141,173,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,245,319,389,457,529,599,660,734,807,868,929,991,1055,1117,1178,1246,1346,1406,1472,1545,1614,1671,1723,1785,1857,1933,1998,2057,2116,2176,2236,2296,2356,2416,2476,2536,2596,2656,2716,2775,2835,2895,2955,3015,3075,3135,3195,3255,3315,3375,3434,3494,3554,3613,3672,3731,3790,3849,3908,3943,3978,4033,4096,4151,4209,4266,4316,4377,4434,4468,4503,4538,4608,4679,4796,4997,5107,5308,5437,5509,5576,5874,8780,10845,12605", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,82,83,87,88,89,90,97,140,172,209,216", "endColumns": "68,62,57,73,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "119,182,240,314,384,452,524,594,655,729,802,863,924,986,1050,1112,1173,1241,1341,1401,1467,1540,1609,1666,1718,1780,1852,1928,1993,2052,2111,2171,2231,2291,2351,2411,2471,2531,2591,2651,2711,2770,2830,2890,2950,3010,3070,3130,3190,3250,3310,3370,3429,3489,3549,3608,3667,3726,3785,3844,3903,3938,3973,4028,4091,4146,4204,4261,4311,4372,4429,4463,4498,4533,4603,4674,4791,4992,5102,5303,5432,5504,5571,5869,8775,10840,12600,12977"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,82,86,107,108,112,113,117,125,126,127,134,177,209,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,384,447,505,579,649,717,789,859,920,994,1067,1128,1189,1251,1315,1377,1438,1506,1606,1666,1732,1805,1874,1931,1983,2045,2117,2193,2258,2317,2376,2436,2496,2556,2616,2676,2736,2796,2856,2916,2976,3035,3095,3155,3215,3275,3335,3395,3455,3515,3575,3635,3694,3754,3814,3873,3932,3991,4050,4109,4168,4203,4238,4293,4356,4411,4469,4526,4576,4637,4694,4728,4763,4894,5164,6242,6359,6560,6670,6871,7365,7437,7504,7802,10708,12773,14533", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,82,86,107,111,112,116,117,125,126,133,176,208,245,252", "endColumns": "68,62,57,73,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "379,442,500,574,644,712,784,854,915,989,1062,1123,1184,1246,1310,1372,1433,1501,1601,1661,1727,1800,1869,1926,1978,2040,2112,2188,2253,2312,2371,2431,2491,2551,2611,2671,2731,2791,2851,2911,2971,3030,3090,3150,3210,3270,3330,3390,3450,3510,3570,3630,3689,3749,3809,3868,3927,3986,4045,4104,4163,4198,4233,4288,4351,4406,4464,4521,4571,4632,4689,4723,4758,4793,4959,5230,6354,6555,6665,6866,6995,7432,7499,7797,10703,12768,14528,14905"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\d94a4bb1a6fc6cbf701df443dbd8cef9\\jetified-startup-runtime-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "4964", "endColumns": "82", "endOffsets": "5042"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-de_values-de.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-fa_values-fa.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ko_values-ko.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ro_values-ro.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-af_values-af.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ur_values-ur.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-my_values-my.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-eu_values-eu.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ru_values-ru.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-th_values-th.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-v16_values-v16.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-mn_values-mn.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-or_values-or.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-sk_values-sk.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-lo_values-lo.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-fr_values-fr.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-uz_values-uz.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-nl_values-nl.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-et_values-et.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ml_values-ml.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-v28_values-v28.arsc.flat", "map": [{"source": "E:\\huoli-rk3566\\hl071501\\launcher\\src\\main\\res\\values-v28\\styles.xml", "from": {"startLines": "2", "startColumns": "0", "startOffsets": "53", "endLines": "4", "endColumns": "8", "endOffsets": "229"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "8", "endOffsets": "229"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-fi_values-fi.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-tr_values-tr.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-hu_values-hu.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-sr_values-sr.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-is_values-is.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-sw_values-sw.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-in_values-in.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-v23_values-v23.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\083e340af32f320e6ad3917bcf6db07c\\work-runtime-2.6.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ca_values-ca.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-da_values-da.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-vi_values-vi.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-pt_values-pt.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-cs_values-cs.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-hi_values-hi.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ms_values-ms.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-v21_values-v21.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,354,470,596,722,850,1022", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,465,591,717,845,1017,1355"}, "to": {"startLines": "2,3,4,5,8,9,10,11,12,15", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,458,574,700,826,954,1126", "endLines": "2,3,4,5,8,9,10,11,14,19", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,569,695,821,949,1121,1459"}}, {"source": "E:\\huoli-rk3566\\hl071501\\launcher\\src\\main\\res\\values-v21\\styles.xml", "from": {"startLines": "2", "startColumns": "0", "startOffsets": "53", "endLines": "3", "endColumns": "8", "endOffsets": "153"}, "to": {"startLines": "6", "startColumns": "4", "startOffsets": "354", "endLines": "7", "endColumns": "8", "endOffsets": "453"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-tl_values-tl.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-kn_values-kn.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-bs_values-bs.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "203", "endOffsets": "254"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-si_values-si.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ta_values-ta.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-hy_values-hy.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-be_values-be.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-iw_values-iw.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-hr_values-hr.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ar_values-ar.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-nb_values-nb.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-as_values-as.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-es_values-es.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-az_values-az.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-sv_values-sv.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-bg_values-bg.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-mk_values-mk.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-bn_values-bn.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ky_values-ky.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ka_values-ka.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-sl_values-sl.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-sq_values-sq.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-it_values-it.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-ja_values-ja.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-te_values-te.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "E:\\huoli-rk3566\\hl071501\\launcher\\build\\intermediates\\res\\merged\\debug\\values-el_values-el.arsc.flat", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}]}