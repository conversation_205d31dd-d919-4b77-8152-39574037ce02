package com.unity3d.player;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.app.DownloadManager;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.core.content.FileProvider;
import java.io.File;

public class UpdateManager {
    private String DOWNLOAD_URL;
    private String version;
    private String content;
    private Context context;
    private ProgressDialog progressDialog;
    private DownloadManager downloadManager;
    private long downloadId;
    private boolean isDownloading = false;
    private Thread downloadThread;

    public UpdateManager(Context context) {
        this.context = context;
        this.downloadManager = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);
    }

    public void setDownloadUrl(String url,String version,String content) {
        this.DOWNLOAD_URL = url;
        this.version = version;
        this.content = content;
    }

    public void checkUpdate(boolean isForce) {
        if (!isNetworkAvailable()) {
            showToast("The network is unavailable, please check the network connection");
            return;
        }
//        AlertDialog.Builder builder = new AlertDialog.Builder(context);
//        builder.setTitle("new version found")
//                .setMessage("Whether to update immediately？")
//                .setPositiveButton("renew", (dialog, which) -> startDownload());
//        if (!isForce) {
//            builder.setNegativeButton("Cancel", null);
//        }
//        builder.show();
        UpdateDialog updateDialog = new UpdateDialog(context);
        TextView tv_version = updateDialog.findViewById(R.id.tv_version);
        TextView tv_update_content = updateDialog.findViewById(R.id.tv_update_content);
        tv_version.setText("V"+version);
        tv_update_content.setText(content);
        Button btnUpdateNow = updateDialog.findViewById(R.id.btn_update_now);
        Button btnNotNow = updateDialog.findViewById(R.id.btn_not_now);
        LinearLayout notupdate_layout = updateDialog.findViewById(R.id.notupdate_layout);
        Button ex_now = updateDialog.findViewById(R.id.ex_now);
        if (isForce) {
            notupdate_layout.setVisibility(View.GONE);
            ex_now.setVisibility(View.VISIBLE);
        } else {
            notupdate_layout.setVisibility(View.VISIBLE);
            ex_now.setVisibility(View.GONE);
        }
        btnUpdateNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateDialog.dismiss();
                startDownload();
            }
        });
        ex_now.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateDialog.dismiss();
                startDownload();
            }
        });
        btnNotNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateDialog.dismiss();
            }
        });
        updateDialog.show();
    }

    private void startDownload() {
        if (isDownloading) {
            showToast("Downloading, please wait...");
            return;
        }
        setupProgressDialog();
        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(DOWNLOAD_URL));
        request.setTitle("New version download");
        request.setDescription("Downloading new version...");
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE);
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, "app-update.apk");
        request.setAllowedOverMetered(true);
        request.setAllowedOverRoaming(false);
        downloadId = downloadManager.enqueue(request);
        isDownloading = true;
        downloadThread = new Thread(this::monitorDownload);
        downloadThread.start();
    }

    private void monitorDownload() {
        while (isDownloading) {
            if (!isNetworkAvailable()) {
                handleDownloadError("Network connection lost");
                break;
            }

            DownloadManager.Query query = new DownloadManager.Query();
            query.setFilterById(downloadId);
            try (Cursor cursor = downloadManager.query(query)) {
                if (cursor != null && cursor.moveToFirst()) {
                    int status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS));
                    switch (status) {
                        case DownloadManager.STATUS_SUCCESSFUL:
                            isDownloading = false;
                            installApk();
                            break;
                        case DownloadManager.STATUS_FAILED:
                            handleDownloadError("Download failed, please try again");
                            break;
                        case DownloadManager.STATUS_RUNNING:
                            updateDownloadProgress(cursor);
                            break;
                    }
                }
            }

            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    private void handleDownloadError(String errorMessage) {
        isDownloading = false;
        downloadManager.remove(downloadId);
        ((Activity)context).runOnUiThread(() -> {
            progressDialog.dismiss();
            showToast(errorMessage);
        });
    }

    private void setupProgressDialog() {
        progressDialog = new ProgressDialog(context);
        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        progressDialog.setTitle("Downloading");
//        progressDialog.setMessage("Please wait...");
        progressDialog.setCancelable(false);
        progressDialog.setProgressDrawable(
                context.getResources().getDrawable(R.drawable.custom_progress_horizontal, null)
        );
//        progressDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "Cancel", (dialog, which) -> {
//            downloadManager.remove(downloadId);
//            isDownloading = false;
//            if (downloadThread != null) {
//                downloadThread.interrupt();
//            }
//            showToast("Download canceled");
//        });
        progressDialog.show();
    }

    private void updateDownloadProgress(Cursor cursor) {
        int bytesDownloaded = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR));
        int bytesTotal = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES));
        if (bytesTotal > 0) {
            int progress = (int) ((bytesDownloaded * 100L) / bytesTotal);
            ((Activity)context).runOnUiThread(() -> progressDialog.setProgress(progress));
        }
    }

    private boolean isNetworkAvailable() {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm == null) return false;
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        return activeNetwork != null && activeNetwork.isConnected();
    }

    private void showToast(String message) {
        ((Activity)context).runOnUiThread(() ->
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        );
    }
    private void installApk() {
        File file = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "app-update.apk");
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setDataAndType(getFileUri(file), "application/vnd.android.package-archive");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        context.startActivity(intent);
        progressDialog.dismiss();
    }

    private Uri getFileUri(File file) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return FileProvider.getUriForFile(context, context.getPackageName() + ".fileprovider", file);
        }
        return Uri.fromFile(file);
    }
}