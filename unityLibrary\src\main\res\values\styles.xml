<?xml version="1.0" encoding="utf-8"?>
<resources>
<style name="UnityThemeSelector" parent="BaseUnityTheme">
	<item name="android:windowBackground">@android:color/black</item>
</style>
<style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen">
</style>
<style name="UnityThemeSelector.Translucent" parent="@style/UnityThemeSelector">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
</style>
    <style name="PrivacyThemeDialog" parent="@android:style/Theme.Dialog">
        <!--Dialog的windowFrame框为无-->
        <item name="android:windowFrame">@null</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsFloating">true</item>
        <!--是否半透明-->
        <item name="android:windowIsTranslucent">true</item>
        <!--是否显示title-->
        <item name="android:windowNoTitle">false</item>
        <!--设置dialog的背景，（#00000000）-->
        <item name="android:background">@android:color/transparent</item>
        <!--显示区域背景是否透明，（#00000000）-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--就是用来控制灰度的值，当为1时，界面除了我们的dialog内容是高亮显示的，dialog以外的区域是黑色的，完全看不到其他内容，系统的默认值是0.5-->
        <item name="android:backgroundDimAmount">0.5</item>
        <!--显示区域以外是否使用黑色半透明背景-->
        <item name="android:backgroundDimEnabled">true</item>
    </style>
</resources>
