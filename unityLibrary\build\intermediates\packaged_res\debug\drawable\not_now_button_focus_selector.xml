<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true"
        android:drawable="@drawable/not_now_img"/> <!-- 按下时的背景 -->

    <!-- 焦点状态（遥控器选中） -->
    <item android:state_focused="true"
        android:drawable="@drawable/not_now_img"/> <!-- 获得焦点时的背景 -->

    <!-- 默认状态 -->
    <item android:drawable="@drawable/not_now_img"/> <!-- 默认背景 -->
</selector>