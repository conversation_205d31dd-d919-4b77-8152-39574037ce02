# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.multidex"
    artifactId: "multidex"
    version: "2.0.0"
  }
  digests {
    sha256: "\300\027\000\t\020r\340\377]\216\302\320\016\254k\217\226\352\030d`\200B^\234\343\306\247\265\366n3"
  }
}
library {
  maven_library {
    artifactId: "CYMobileJNI-release"
  }
  digests {
    sha256: "\252\377\2708*\031\005\214\341\302\a\221\004\306+\027^`\"#\3146W\265\367\275\342\332\264\310\036\247"
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.2.0"
  }
  digests {
    sha256: "RK\213\210\316\266\247J~D\346\265g\2415f\017!\027\231\220L\262\030\277\356[\341\026h \262"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.1.0"
  }
  digests {
    sha256: "\323\215c\355\263\017\024g\201\215P\252\360_\212i-\352\21319*\004\233\372\231\033\025\232\325\266\222"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.0.0"
  }
  digests {
    sha256: "\344\257\311\3466\030?o>\016\337\034\364a!\244\222\377\322\306s\a[\260\177U\307\251\235\324<\373"
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.0"
  }
  digests {
    sha256: "\232\035w\024\n\302\"\267\206kPT\356}\025\233\301\200\t\207\355-F\335j\375\321E\253\267\020\301"
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.0.0"
  }
  digests {
    sha256: "\234\215\021{\\+\301 \241\315\376\270W\340[I[\026\303`\023W\003r\247\b\367\202~:\311\371"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.0.0"
  }
  digests {
    sha256: "{\255z\030\210\004\255\352o\241\363]^\371\233p_ \275\223\354\255\336HG`\377\206\2655\376\374"
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.0.0"
  }
  digests {
    sha256: "K\200\2637w\233Rnd\260\356\f\251\340\337C\270\b4M\024_\216\233\034B\2414\332\305z\330"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.7.2"
  }
  digests {
    sha256: "kd\312\035 iu\022B\230E7\325\336,\276bz\340\000C\017Q\027\304}\214\263\322r\372\315"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "2.6.0"
  }
  digests {
    sha256: "M\204\357hbw\265\216\260V\221\254\031\315;\357\243B\232\'\'I\202\356e\352\017\a\004K\314\000"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.3.71"
  }
  digests {
    sha256: "Z\316\"\261\002\251d%\344\254D\340U\213\222\1778W\265j3\313\302\211\317\033p\256\346E\346\247"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.3.71"
  }
  digests {
    sha256: "\227O\212\233{\374\343\3270\250n\376\016\253!\232rb\036\2050\371\0360\310\237@\v\251\200\222\354"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.8.6"
  }
  digests {
    sha256: "\310\373H9\005M(\v03\370\000\321\365\251}\342\360(\353\213\242\353E\212\322\207\3456\363\362_"
  }
}
library {
  maven_library {
    groupId: "com.umeng.umsdk"
    artifactId: "common"
    version: "9.8.6"
  }
  digests {
    sha256: "\337gP1W\251\376\342m9\026\273T\2160\374\2657\035\200\rsZ\342\342\a5x_kU\272"
  }
}
library {
  maven_library {
    groupId: "com.umeng.umsdk"
    artifactId: "asms"
    version: "1.8.7.2"
  }
  digests {
    sha256: "g\241\272\364\031\210\344\2219\254\3152\3512]\317\310\211Y\f1%\237\0276`\310k\2319\202-"
  }
}
library {
  maven_library {
    groupId: "com.umeng.umsdk"
    artifactId: "apm"
    version: "2.0.4"
  }
  digests {
    sha256: "t!\230\240\224\300\334\333:\240\203\'t\371\342\034\024!\370\v\307\201\237\273\031\330\367\237Y\332\002\352"
  }
}
library {
  digests {
    sha256: "\254\025\330b\361r\267pwm\320\275)\n\374\232\312\244iT\372E\025L\022\005\263\311\370pr\250"
  }
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 4
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 3
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 8
  library_dep_index: 3
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
  library_dep_index: 6
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
  library_dep_index: 12
}
library_dependencies {
  library_index: 11
  library_dep_index: 12
  library_dep_index: 13
}
library_dependencies {
  library_index: 18
}
module_dependencies {
  module_name: "base"
  dependency_index: 18
}
