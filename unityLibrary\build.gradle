// GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN

apply plugin: 'com.android.library'


dependencies {
    implementation 'com.android.support:multidex:1.0.3'
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation(name: 'CYMobileJNI-release', ext: 'aar')
//    implementation project(':CYMobileJNI')
    implementation 'androidx.core:core:1.2.0'
    implementation 'com.squareup.okhttp3:okhttp:4.7.2'
    implementation 'com.squareup.okio:okio:2.6.0'
    implementation 'com.google.code.gson:gson:2.8.6'
    // 友盟基础组件库（所有友盟业务SDK都依赖基础组件库）
    implementation 'com.umeng.umsdk:common:+'//必选
    implementation 'com.umeng.umsdk:asms:+'//必选
    implementation 'com.umeng.umsdk:apm:+'// U-APM产品包依赖，必选
    implementation 'androidx.work:work-runtime:2.6.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.3.1'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.3.1'
}

android {
    compileSdkVersion 30
    buildToolsVersion '30.0.3'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 30
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
        versionCode 31
        versionName '2.0'
        consumerProguardFiles 'proguard-unity.txt'
    }

    lintOptions {
        abortOnError false
    }

    aaptOptions {
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

    packagingOptions {
        doNotStrip '*/armeabi-v7a/*.so'
        doNotStrip '*/arm64-v8a/*.so'
    }
}

