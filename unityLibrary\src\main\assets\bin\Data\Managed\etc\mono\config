<configuration>
	<dllmap dll="i:cygwin1.dll" target="libc.dylib" os="!windows" />
	<dllmap dll="libc" target="libc.dylib" os="!windows"/>
	<dllmap dll="intl" target="libintl.dylib" os="!windows"/>
	<dllmap dll="intl" name="bind_textdomain_codeset" target="libc.dylib" os="solaris"/>
	<dllmap dll="libintl" name="bind_textdomain_codeset" target="libc.dylib" os="solaris"/>
	<dllmap dll="libintl" target="libintl.dylib" os="!windows"/>
	<dllmap dll="i:libxslt.dll" target="libxslt.dylib" os="!windows"/>
	<dllmap dll="i:odbc32.dll" target="libodbc.dylib" os="!windows"/>
	<dllmap dll="i:odbc32.dll" target="libiodbc.dylib" os="osx"/>
	<dllmap dll="oci" target="libclntsh.dylib" os="!windows"/>
	<dllmap dll="db2cli" target="libdb2_36.dylib" os="!windows"/>
	<dllmap dll="MonoPosixHelper" target="libMonoPosixHelper.dylib" os="!windows" />
	<dllmap dll="libmono-btls-shared" target="libmono-btls-shared.dylib" os="!windows" />
	<dllmap dll="i:msvcrt" target="libc.dylib" os="!windows"/>
	<dllmap dll="i:msvcrt.dll" target="libc.dylib" os="!windows"/>
	<dllmap dll="sqlite" target="libsqlite.0.dylib" os="!windows"/>
	<dllmap dll="sqlite3" target="libsqlite3.0.dylib" os="!windows"/>
	<dllmap dll="libX11" target="libX11.dylib" os="!windows" />
	<dllmap dll="libgdk-x11-2.0" target="libgdk-x11-2.0.dylib" os="!windows"/>
	<dllmap dll="libgdk_pixbuf-2.0" target="libgdk_pixbuf-2.0.so.0" os="!windows"/>
	<dllmap dll="libgtk-x11-2.0" target="libgtk-x11-2.0.dylib" os="!windows"/>
	<dllmap dll="libglib-2.0" target="libglib-2.0.so.0" os="!windows"/>
	<dllmap dll="libgobject-2.0" target="libgobject-2.0.so.0" os="!windows"/>
	<dllmap dll="libgnomeui-2" target="libgnomeui-2.so.0" os="!windows"/>
	<dllmap dll="librsvg-2" target="librsvg-2.so.2" os="!windows"/>
	<dllmap dll="libXinerama" target="libXinerama.so.1" os="!windows" />
	<dllmap dll="libasound" target="libasound.so.2" os="!windows" />
	<dllmap dll="libcairo-2.dll" target="libcairo.so.2" os="!windows"/>
	<dllmap dll="libcairo-2.dll" target="libcairo.2.dylib" os="osx"/>
	<dllmap dll="libcups" target="libcups.so.2" os="!windows"/>
	<dllmap dll="libcups" target="libcups.dylib" os="osx"/>
	<dllmap dll="i:kernel32.dll">
		<dllentry dll="__Internal" name="CopyMemory" target="mono_win32_compat_CopyMemory"/>
		<dllentry dll="__Internal" name="FillMemory" target="mono_win32_compat_FillMemory"/>
		<dllentry dll="__Internal" name="MoveMemory" target="mono_win32_compat_MoveMemory"/>
		<dllentry dll="__Internal" name="ZeroMemory" target="mono_win32_compat_ZeroMemory"/>
	</dllmap>
	<dllmap dll="gdiplus" target="/Users/<USER>/build/output/Unity-Technologies/mono/external/buildscripts/add_to_build_results/monodistribution/lib/libgdiplus.dylib" os="!windows"/>
	<dllmap dll="gdiplus.dll" target="/Users/<USER>/build/output/Unity-Technologies/mono/external/buildscripts/add_to_build_results/monodistribution/lib/libgdiplus.dylib"  os="!windows"/>
	<dllmap dll="gdi32" target="/Users/<USER>/build/output/Unity-Technologies/mono/external/buildscripts/add_to_build_results/monodistribution/lib/libgdiplus.dylib" os="!windows"/>
	<dllmap dll="gdi32.dll" target="/Users/<USER>/build/output/Unity-Technologies/mono/external/buildscripts/add_to_build_results/monodistribution/lib/libgdiplus.dylib" os="!windows"/>
</configuration>
