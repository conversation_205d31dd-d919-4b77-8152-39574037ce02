package com.unity3d.player;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.os.Build;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

/**
 * Modern network monitoring utility that handles deprecated APIs
 */
public class NetworkMonitor {
    
    private Context context;
    private ConnectivityManager connectivityManager;
    private NetworkCallback networkCallback;
    private NetworkStateListener listener;
    
    public interface NetworkStateListener {
        void onNetworkAvailable();
        void onNetworkLost();
    }
    
    public NetworkMonitor(Context context) {
        this.context = context;
        this.connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
    }
    
    /**
     * Check if network is currently available
     */
    public boolean isNetworkAvailable() {
        if (connectivityManager == null) {
            return false;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return isNetworkAvailableApi23();
        } else {
            return isNetworkAvailableLegacy();
        }
    }
    
    @RequiresApi(api = Build.VERSION_CODES.M)
    private boolean isNetworkAvailableApi23() {
        Network activeNetwork = connectivityManager.getActiveNetwork();
        if (activeNetwork == null) {
            return false;
        }
        
        NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
        if (capabilities == null) {
            return false;
        }
        
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
               capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
    }
    
    @SuppressWarnings("deprecation")
    private boolean isNetworkAvailableLegacy() {
        NetworkInfo activeNetwork = connectivityManager.getActiveNetworkInfo();
        return activeNetwork != null && activeNetwork.isConnected();
    }
    
    /**
     * Get network type description
     */
    public String getNetworkType() {
        if (connectivityManager == null) {
            return "Unknown";
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return getNetworkTypeApi23();
        } else {
            return getNetworkTypeLegacy();
        }
    }
    
    @RequiresApi(api = Build.VERSION_CODES.M)
    private String getNetworkTypeApi23() {
        Network activeNetwork = connectivityManager.getActiveNetwork();
        if (activeNetwork == null) {
            return "No Connection";
        }
        
        NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
        if (capabilities == null) {
            return "Unknown";
        }
        
        if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
            return "WiFi";
        } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
            return "Mobile Data";
        } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
            return "Ethernet";
        } else {
            return "Other";
        }
    }
    
    @SuppressWarnings("deprecation")
    private String getNetworkTypeLegacy() {
        NetworkInfo activeNetwork = connectivityManager.getActiveNetworkInfo();
        if (activeNetwork == null) {
            return "No Connection";
        }
        
        switch (activeNetwork.getType()) {
            case ConnectivityManager.TYPE_WIFI:
                return "WiFi";
            case ConnectivityManager.TYPE_MOBILE:
                return "Mobile Data";
            case ConnectivityManager.TYPE_ETHERNET:
                return "Ethernet";
            default:
                return "Other";
        }
    }
    
    /**
     * Start monitoring network changes
     */
    public void startMonitoring(NetworkStateListener listener) {
        this.listener = listener;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            startMonitoringApi24();
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            startMonitoringApi21();
        }
        // For API < 21, we don't register callbacks as the APIs are too limited
    }
    
    @RequiresApi(api = Build.VERSION_CODES.N)
    private void startMonitoringApi24() {
        if (networkCallback != null) {
            return; // Already monitoring
        }
        
        networkCallback = new NetworkCallback();
        connectivityManager.registerDefaultNetworkCallback(networkCallback);
    }
    
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private void startMonitoringApi21() {
        if (networkCallback != null) {
            return; // Already monitoring
        }
        
        NetworkRequest.Builder builder = new NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        
        networkCallback = new NetworkCallback();
        connectivityManager.registerNetworkCallback(builder.build(), networkCallback);
    }
    
    /**
     * Stop monitoring network changes
     */
    public void stopMonitoring() {
        if (networkCallback != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            connectivityManager.unregisterNetworkCallback(networkCallback);
            networkCallback = null;
        }
        listener = null;
    }
    
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private class NetworkCallback extends ConnectivityManager.NetworkCallback {
        @Override
        public void onAvailable(@NonNull Network network) {
            if (listener != null) {
                listener.onNetworkAvailable();
            }
        }
        
        @Override
        public void onLost(@NonNull Network network) {
            if (listener != null) {
                listener.onNetworkLost();
            }
        }
    }
}
