// GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN

apply plugin: 'com.android.application'

dependencies {
    implementation project(':unityLibrary')
}

android {
    compileSdkVersion 30
    buildToolsVersion '30.0.3'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        multiDexEnabled true
        minSdkVersion 21
        targetSdkVersion 30
        applicationId 'com.cy.exercisetv'
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
        versionCode 31
        versionName '1.3.2'
    }

    signingConfigs {
        release {
            storeFile file('D:/gamekey/bainian.jks')
            storePassword 'bn123456'
            keyAlias 'bainian'
            keyPassword 'bn123456'
        }

        debug {
            storeFile file('D:/gamekey/bainian.jks')
            storePassword 'bn123456'
            keyAlias 'bainian'
            keyPassword 'bn123456'
        }
    }

    aaptOptions {
        noCompress = ['.unity3d', '.ress', '.resource', '.obb', 'android/android', '.manifest', 'android/img']
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

    lintOptions {
        abortOnError false
    }

    buildTypes {
        debug {
            minifyEnabled false
            useProguard false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            signingConfig signingConfigs.debug
            jniDebuggable true
        }
        release {
            minifyEnabled false
            useProguard false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            signingConfig signingConfigs.debug
        }
    }

    packagingOptions {
        doNotStrip '*/armeabi-v7a/*.so'
        doNotStrip '*/arm64-v8a/*.so'
    }

    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = false
        }
        abi {
            enableSplit = true
        }
    }
}
