// GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN

allprojects {
    buildscript {
        repositories {
            maven { url 'https://repo1.maven.org/maven2/'} //友盟,上下两处repositories都要加
            mavenCentral()
            google()
            jcenter()
        }

        dependencies {
            classpath 'com.google.gms:google-services:4.3.3'
            classpath 'com.android.tools.build:gradle:4.2.1'
            
        }
    }

    repositories {
            maven { url 'https://repo1.maven.org/maven2/'} //友盟,上下两处repositories都要加
            mavenCentral()
        google()
        jcenter()
        flatDir {
            dirs "${project(':unityLibrary').projectDir}/libs"
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
