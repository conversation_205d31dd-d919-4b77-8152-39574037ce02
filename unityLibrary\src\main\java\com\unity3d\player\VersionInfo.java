package com.unity3d.player;

/**
 * author : admin
 * date : 2021/8/26 18:10
 * description :
 */
public class VersionInfo {

    /**
     * error : 0
     * msg : success
     * data : {"version":"v1.0.1","version_explain":"版本初始化","if_forced_update":1,"version_status":1,"visitor_status":0,"apk_url":"http://www.baidu.com"}
     */

    private int error;
    private String msg;
    private DataBean data;

    public int getError() {
        return error;
    }

    public void setError(int error) {
        this.error = error;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * version : v1.0.1
         * version_explain : 版本初始化
         * if_forced_update : 1
         * version_status : 1
         * visitor_status : 0
         * apk_url : http://www.baidu.com
         */

        private String version;
        private String version_explain;
        private int if_forced_update;
        private int version_status;
        private int visitor_status;
        private String apk_url;

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getVersion_explain() {
            return version_explain;
        }

        public void setVersion_explain(String version_explain) {
            this.version_explain = version_explain;
        }

        public int getIf_forced_update() {
            return if_forced_update;
        }

        public void setIf_forced_update(int if_forced_update) {
            this.if_forced_update = if_forced_update;
        }

        public int getVersion_status() {
            return version_status;
        }

        public void setVersion_status(int version_status) {
            this.version_status = version_status;
        }

        public int getVisitor_status() {
            return visitor_status;
        }

        public void setVisitor_status(int visitor_status) {
            this.visitor_status = visitor_status;
        }

        public String getApk_url() {
            return apk_url;
        }

        public void setApk_url(String apk_url) {
            this.apk_url = apk_url;
        }
    }
}
